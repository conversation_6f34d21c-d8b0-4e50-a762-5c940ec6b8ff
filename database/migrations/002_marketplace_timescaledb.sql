-- =====================================================
-- Migration 002: TimescaleDB Hypertables for Marketplace
-- Description: Create time-series tables for cross-business events and attribution
-- Dependencies: Migration 001, TimescaleDB extension
-- Estimated Duration: 3-5 minutes
-- =====================================================

BEGIN;

-- Ensure TimescaleDB extension is available
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create cross-business events hypertable
CREATE TABLE IF NOT EXISTS cross_business_events (
  time TIMESTAMPTZ NOT NULL,
  source_tenant_id UUID NOT NULL,
  target_tenant_id UUID NOT NULL,
  partnership_id UUID,
  customer_id UUID,
  
  -- Event details
  event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
    'referral_click', 'referral_view', 'conversion', 'revenue', 'signup', 'engagement'
  )),
  event_data JSONB DEFAULT '{}',
  
  -- Attribution data
  revenue DECIMAL(10,2) DEFAULT 0.00,
  commission_amount DECIMAL(10,2) DEFAULT 0.00,
  attribution_model VARCHAR(30) DEFAULT 'last_touch' CHECK (attribution_model IN (
    'first_touch', 'last_touch', 'linear', 'time_decay', 'position_based'
  )),
  attribution_weight DECIMAL(5,4) DEFAULT 1.0000,
  
  -- Tracking metadata
  source_url TEXT,
  referrer_url TEXT,
  user_agent TEXT,
  ip_address INET,
  session_id UUID,
  
  -- Performance tracking
  processing_time_ms INTEGER,
  
  -- Constraints
  FOREIGN KEY (source_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (target_tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (partnership_id) REFERENCES marketplace_partnerships(id) ON DELETE SET NULL,
  CONSTRAINT valid_revenue CHECK (revenue >= 0),
  CONSTRAINT valid_commission CHECK (commission_amount >= 0),
  CONSTRAINT different_tenants CHECK (source_tenant_id != target_tenant_id),
  CONSTRAINT valid_attribution_weight CHECK (attribution_weight >= 0 AND attribution_weight <= 1)
);

-- Convert to hypertable (7-day chunks for optimal performance)
SELECT create_hypertable(
  'cross_business_events', 
  'time',
  chunk_time_interval => INTERVAL '7 days',
  if_not_exists => TRUE
);

-- Create indexes for cross-business events
CREATE INDEX IF NOT EXISTS idx_cross_events_source_time
  ON cross_business_events(source_tenant_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_cross_events_target_time
  ON cross_business_events(target_tenant_id, time DESC);
CREATE INDEX IF NOT EXISTS idx_cross_events_partnership
  ON cross_business_events(partnership_id, time DESC) WHERE partnership_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cross_events_customer
  ON cross_business_events(customer_id, time DESC) WHERE customer_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cross_events_type
  ON cross_business_events(event_type, time DESC);
CREATE INDEX IF NOT EXISTS idx_cross_events_revenue
  ON cross_business_events(time DESC, revenue) WHERE revenue > 0;

-- Create partner compatibility scores table
CREATE TABLE IF NOT EXISTS partner_compatibility_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_a_id UUID NOT NULL,
  tenant_b_id UUID NOT NULL,
  
  -- Compatibility metrics (0.00 to 100.00)
  overall_score DECIMAL(5,2) NOT NULL,
  customer_overlap_score DECIMAL(5,2) DEFAULT 0.00,
  seasonal_alignment_score DECIMAL(5,2) DEFAULT 0.00,
  clv_compatibility_score DECIMAL(5,2) DEFAULT 0.00,
  funnel_synergy_score DECIMAL(5,2) DEFAULT 0.00,
  geographic_alignment_score DECIMAL(5,2) DEFAULT 0.00,
  
  -- ML model metadata
  model_version VARCHAR(20) NOT NULL DEFAULT 'v1.0',
  confidence_level DECIMAL(5,2) DEFAULT 0.00,
  calculation_date TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),
  
  -- Detailed insights
  insights JSONB DEFAULT '{}',
  recommendation_reasons TEXT[],
  
  -- Performance tracking
  calculation_time_ms INTEGER,
  
  -- Constraints
  FOREIGN KEY (tenant_a_id) REFERENCES tenants(id) ON DELETE CASCADE,
  FOREIGN KEY (tenant_b_id) REFERENCES tenants(id) ON DELETE CASCADE,
  CONSTRAINT different_tenants_compat CHECK (tenant_a_id != tenant_b_id),
  CONSTRAINT valid_overall_score CHECK (overall_score >= 0 AND overall_score <= 100),
  CONSTRAINT valid_component_scores CHECK (
    customer_overlap_score >= 0 AND customer_overlap_score <= 100 AND
    seasonal_alignment_score >= 0 AND seasonal_alignment_score <= 100 AND
    clv_compatibility_score >= 0 AND clv_compatibility_score <= 100 AND
    funnel_synergy_score >= 0 AND funnel_synergy_score <= 100 AND
    geographic_alignment_score >= 0 AND geographic_alignment_score <= 100
  ),
  CONSTRAINT valid_confidence CHECK (confidence_level >= 0 AND confidence_level <= 100)
);

-- Create unique constraint for compatibility scores (prevent duplicates)
CREATE UNIQUE INDEX IF NOT EXISTS idx_compatibility_unique
  ON partner_compatibility_scores(
    LEAST(tenant_a_id, tenant_b_id),
    GREATEST(tenant_a_id, tenant_b_id)
  );

-- Create indexes for compatibility scores
CREATE INDEX IF NOT EXISTS idx_compatibility_tenant_a
  ON partner_compatibility_scores(tenant_a_id, overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_compatibility_tenant_b
  ON partner_compatibility_scores(tenant_b_id, overall_score DESC);
CREATE INDEX IF NOT EXISTS idx_compatibility_score
  ON partner_compatibility_scores(overall_score DESC, calculation_date DESC);
CREATE INDEX IF NOT EXISTS idx_compatibility_expires
  ON partner_compatibility_scores(expires_at) WHERE expires_at > NOW();

-- Network insights cache
CREATE TABLE IF NOT EXISTS network_insights_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  insight_type VARCHAR(50) NOT NULL, -- 'industry_benchmark', 'trend_analysis', 'opportunity'
  tenant_id UUID, -- NULL for global insights
  
  -- Insight data
  insight_data JSONB NOT NULL,
  metadata JSONB,
  
  -- Cache management
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  cache_key VARCHAR(255) UNIQUE,
  
  -- Performance tracking
  generation_time_ms INTEGER,
  access_count INTEGER DEFAULT 0,
  
  FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- Indexes for cache performance
CREATE INDEX IF NOT EXISTS idx_insights_cache_type
  ON network_insights_cache(insight_type, expires_at);
CREATE INDEX IF NOT EXISTS idx_insights_cache_tenant
  ON network_insights_cache(tenant_id, insight_type);
CREATE INDEX IF NOT EXISTS idx_insights_cache_key
  ON network_insights_cache(cache_key);

COMMIT;

-- Verify TimescaleDB hypertable creation
SELECT 'Migration 002 completed successfully' as status;
SELECT hypertable_name, num_chunks FROM timescaledb_information.hypertables 
WHERE hypertable_name = 'cross_business_events';
